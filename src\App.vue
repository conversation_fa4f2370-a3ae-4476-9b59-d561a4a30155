<template>
  <div id="app" :style="appStyleObj">
    <div id="loader-global" v-if="loading || globalLoading">
      <div class="loader-section"></div>
    </div>
    <router-view />
    <!-- 保证页面加载就插入样式#chalk-style -->
    <ThemePicker v-show="false"></ThemePicker>
  </div>
</template>

<script>
import Vue from "vue";
import ThemePicker from "@/components/ThemePicker";
import setTheme from "./components/mixin/setTheme";
import { getSysConfig } from "@/api/common";
import { getDefaultThemeObj } from "./api/theme";
import { mapActions, mapGetters } from "vuex";
import { setSession } from "@/utils/session";
import { isFinishedInit } from "@/api/register";
import { getProductInfo } from "@/api/common";
export default {
  name: "App",
  components: {
    ThemePicker,
  },
  mixins: [setTheme],
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      webTitle: this.$store.state.settings.webTitle,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${this.$store.state.settings.webTitle}`
          : this.$store.state.settings.webTitle;
      },
    };
  },
  beforeCreate() {
    window.$eventBus = new Vue();
  },
  data() {
    return {
      themeSystem: JSON.parse(
        JSON.stringify(this.$store.state.settings.themeSystem),
      ), // 默认系统主题[]
      loading: false,
      productInfo: {},
    };
  },
  computed: {
    ...mapGetters(["version", "loginImg", "loginLogo", "sysImg", "sysLogo"]),
    globalLoading() {
      return this.$store.state.app.globalLoading;
    },
  },
  provide() {
    return { getDefaultTheme: this.getDefaultTheme };
  },
  watch: {
    theme: {
      handler(val) {
        this.setTheme(val);
      },
      immediate: true,
    },
  },
  methods: {
    ...mapActions(["getSysImg", "getThemeObj", "GetInfo", "getPortalInfo"]),
    getDefaultLanguage() {
      getSysConfig()
        .then(async (res) => {
          this.$store.commit("SET_SYS_VERSION", res.data.systemVersion);
          // this.$store.commit("SET_PRODUCT_NAME", res.data?.productName ?? null);
          setSession(
            "UKeyInsertStatus",
            res.data.UKeyInsertStatus ? res.data.UKeyInsertStatus : 0,
          ); // 0: 关闭uk拔掉退出登录校验  1：开启uk拔掉退出登录校验
          if (!this.getLanguage()) {
            let language = "zh";
            if (res.data.language) {
              language = res.data.language == "zh_CN" ? "zh" : "en";
              this.$i18n.locale = language;
            }
            this.setLanguage(language);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getFavicon(url) {
      var icon_url = url || "favicon.ico";
      var link =
        document.querySelector("link[rel*='icon']") ||
        document.createElement("link");
      link.type = "image/x-icon";
      link.rel = "shortcut icon";
      link.href = icon_url;
      document.getElementsByTagName("head")[0].appendChild(link);
    },
    async getDefaultTheme(isLoading = true) {
      try {
        // isLoading开启白屏(变换logo图片需要时间)
        this.loading = isLoading ? true : false;
        let id = ""; // 判断主题id为本地还是系统
        let res = await getDefaultThemeObj();
        res.data.themeId = res.data.themeId ? res.data.themeId : "001";
        let proRes = await getProductInfo();
        this.productInfo = proRes.data;

        // 如果本地主题覆盖系统默认主题
        if (
          localStorage.getItem("isCoverSystem") == "true" &&
          localStorage.getItem("layout-setting")
        ) {
          let layoutData = JSON.parse(
            localStorage.getItem("layout-setting") || "{}",
          );
          if (layoutData.themeId) {
            await this.changeLocalTheme(layoutData);
            layoutData.webTitle = this.Zh()
              ? this.productInfo.productCnName
              : this.productInfo.productEnName;
            this.initTheme(layoutData);
            id = layoutData.themeId;
          }
        }
        // 否则,初始化系统样式
        if (!id) {
          id = res.data.themeId;
          let themeObj = res.data.themeNameZh ? res.data : null;
          if (!themeObj) {
            let obj = this.themeSystem.find((item, i) => {
              return item.themeId == id;
            });
            themeObj = obj || this.themeSystem[0];
          }
          themeObj.webTitle = this.isZh()
            ? this.productInfo.productCnName
            : this.productInfo.productEnName;
          this.initTheme(themeObj);
        }
        // 根据主题id获取系统图片
        // await this.getSysImg(id);
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    checkSystemInit() {
      isFinishedInit().then((res) => {
        if (res.data.finish == "N") {
          this.$router.replace("/register");
        }
      });
    },
  },
  async created() {
    if (swGlobal.isInIframe != "true") {
      this.checkSystemInit();
      this.getDefaultLanguage();
      // 先获取themeId,再获取图片
      this.getDefaultTheme();
      this.getPortalInfo();
    } else {
      let val = localStorage.getItem("SecTheme");
      if (val) {
        this.setTheme(val);
      }
    }
  },
};
</script>
<style scoped lang="scss">
#loader-global {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  .loader-section {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    // backdrop-filter: blur(50px);
    background: #fff;
    box-shadow: 0 0 30px 10px rgba(0, 0, 0, 0.3);
  }
}
</style>
